import requests
import json

# Test tạo đề thi 60 câu với bảng đáp án cải thiện
url = "http://127.0.0.1:8000/api/v1/exam/generate-exam-download"

# Request 60 câu như user yêu cầu
payload = {
    "lesson_id": "234",
    "mon_hoc": "<PERSON><PERSON><PERSON> học",
    "lop": 12,
    "tong_so_cau": 60,
    "cau_hinh_de": [
        {
            "bai": "Cấu tạo nguyên tử",
            "so_cau": 60,
            "noi_dung": [
                {
                    "ten_noi_dung": "Hạt cấu tạo nguyên tử",
                    "yeu_cau_can_dat": "Học sinh hiểu và phân biệt được proton, neutron và electron về khối lư<PERSON>, đi<PERSON>n tích, vị trí trong nguyên tử.",
                    "muc_do": [
                        {
                            "loai": "Nhận biết",
                            "so_cau": 25,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Thông hiểu",
                            "so_cau": 20,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Vận dụng",
                            "so_cau": 10,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Vận dụng cao",
                            "so_cau": 5,
                            "loai_cau": ["TN"]
                        }
                    ]
                }
            ]
        }
    ]
}

headers = {
    "Content-Type": "application/json",
    "X-API-Key": "test-api-key-2024"
}

print("Testing 60 questions exam generation with improved answer table...")
print("Request payload prepared...")

try:
    response = requests.post(url, json=payload, headers=headers, timeout=300)  # 5 phút timeout
    print(f"Status code: {response.status_code}")
    
    if response.status_code == 200:
        print("SUCCESS: Exam generated!")
        
        # Lưu file để kiểm tra
        filename = "test_60_questions_improved_format.docx"
        with open(filename, "wb") as f:
            f.write(response.content)
        
        print(f"File saved: {filename}")
        print(f"File size: {len(response.content)} bytes")
        print("\nHãy mở file để kiểm tra:")
        print("1. Có đủ 60 câu hỏi không")
        print("2. Bảng đáp án có format đẹp hơn không (6 hàng x 10 cột)")
        print("3. Có đủ 60 ô đáp án không (kể cả ô trống)")
        
    else:
        print(f"ERROR: {response.status_code}")
        try:
            error_data = response.json()
            print(f"Error detail: {error_data}")
        except:
            print(f"Response text: {response.text[:500]}...")
        
except Exception as e:
    print(f"ERROR: {e}")
