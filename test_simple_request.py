import requests
import json

# Test với request đơn giản hơn - chỉ 1 câu hỏi
url = "http://127.0.0.1:8000/api/v1/exam/generate-exam"

data = {
    "exam_id": "test_simple",
    "ten_truong": "Trường Test",
    "mon_hoc": "<PERSON><PERSON><PERSON> học",
    "lop": 12,
    "tong_so_cau": 1,  # Chỉ tạo 1 câu để test
    "cau_hinh_de": [
        {
            "lesson_id": "234",
            "yeu_cau_can_dat": "Test API key mới",
            "muc_do": [
                {
                    "loai": "Nhận biết",
                    "so_cau": 1,
                    "loai_cau": ["TN"]
                }
            ]
        }
    ]
}

try:
    print("Dang gui request test...")
    response = requests.post(url, json=data, timeout=30)
    print(f"Status Code: {response.status_code}")

    if response.status_code == 200:
        print("SUCCESS: API key moi hoat dong!")
        result = response.json()
        print(f"Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
    else:
        print(f"ERROR: {response.status_code}")
        print(f"Response: {response.text}")

except Exception as e:
    print(f"Exception: {e}")
