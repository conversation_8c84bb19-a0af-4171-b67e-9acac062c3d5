import requests
import json

# Test với request rất đơn giản
url = "http://127.0.0.1:8000/api/v1/exam/generate-exam"

data = {
    "exam_id": "test_final",
    "ten_truong": "Truong Test",
    "mon_hoc": "Hoa hoc",
    "lop": 12,
    "tong_so_cau": 1,  # Chỉ 1 câu
    "cau_hinh_de": [
        {
            "lesson_id": "234",
            "yeu_cau_can_dat": "Test simple",
            "muc_do": [
                {
                    "loai": "Nhận biết",
                    "so_cau": 1,
                    "loai_cau": ["TN"]
                }
            ]
        }
    ]
}

try:
    print("Testing final API...")
    response = requests.post(url, json=data, timeout=60)
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        print("SUCCESS: Exam generation works!")
        result = response.json()
        if 'document_url' in result:
            print(f"Document URL: {result['document_url']}")
        else:
            print("No document URL in response")
    else:
        print(f"ERROR: {response.status_code}")
        # Don't print response text to avoid encoding issues
        
except Exception as e:
    print(f"Exception: {str(e)[:100]}")
