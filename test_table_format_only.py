from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import sys
import os

# Thêm path để import service
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.exam_docx_service import ExamDocxService

def test_answer_table_format():
    """Test format bảng đáp án với dữ liệu giả"""
    
    # Tạo dữ liệu giả cho 60 câu hỏi
    fake_questions = []
    for i in range(60):
        fake_questions.append({
            "loai_cau": "TN",
            "dap_an": {
                "dung": chr(65 + (i % 4))  # A, B, C, D luân phiên
            }
        })
    
    # Tạo document
    doc = Document()
    
    # Tạo service
    service = ExamDocxService()
    
    # Setup document style (bao gồm số trang)
    service._setup_document_style(doc)
    
    # Thêm tiêu đề
    title_para = doc.add_paragraph()
    title_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    title_run = title_para.add_run("TEST BẢNG ĐÁP ÁN 60 CÂU")
    title_run.bold = True
    title_run.font.size = Pt(16)
    
    doc.add_paragraph()
    
    # Tạo bảng đáp án
    service._create_tn_answer_section(doc, fake_questions)
    
    # Lưu file
    filename = "test_table_format_60_questions.docx"
    doc.save(filename)
    
    print(f"✅ File test đã được tạo: {filename}")
    print(f"📊 Số câu hỏi test: {len(fake_questions)}")
    print("\n🔍 Hãy kiểm tra:")
    print("1. Bảng có 7 hàng x 10 cột không?")
    print("2. Có đủ 60 ô đáp án không?")
    print("3. Có số trang ở footer không?")
    print("4. Format có đẹp và dễ đọc không?")
    print("5. Các ô cuối có để trống đúng không?")

if __name__ == "__main__":
    test_answer_table_format()
