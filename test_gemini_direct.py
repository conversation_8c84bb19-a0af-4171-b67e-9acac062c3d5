import os
import requests
import json
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Get API key
api_key = os.getenv("GEMINI_API_KEY")
print(f"Testing API key: {api_key[:20]}...")

# Test direct Gemini API call
url = f"https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key={api_key}"

headers = {
    "Content-Type": "application/json"
}

# Simple test prompt
data = {
    "contents": [
        {
            "parts": [
                {
                    "text": "Tạo 1 câu hỏi trắc nghiệm đơn giản về hóa học. Trả lời bằng tiếng Việt."
                }
            ]
        }
    ],
    "generationConfig": {
        "temperature": 0.7,
        "maxOutputTokens": 500
    }
}

try:
    print("Testing direct Gemini API...")
    response = requests.post(url, headers=headers, json=data, timeout=30)
    
    print(f"Status Code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("SUCCESS: API key works!")
        
        if 'candidates' in result and len(result['candidates']) > 0:
            content = result['candidates'][0]['content']['parts'][0]['text']
            print(f"Response: {content[:200]}...")
        else:
            print("Empty response from Gemini")
            print(f"Full response: {json.dumps(result, indent=2)}")
    else:
        print(f"ERROR: {response.status_code}")
        print(f"Response: {response.text}")
        
except Exception as e:
    print(f"Exception: {e}")
