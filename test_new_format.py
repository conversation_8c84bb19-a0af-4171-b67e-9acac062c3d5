"""
Test script cho format mới của exam generation
"""
import requests
import json

# Test data với format mới
test_request = {
    "exam_id": "hoa12_de_60cau",
    "ten_truong": "Trường THPT <PERSON><PERSON><PERSON>",
    "mon_hoc": "<PERSON><PERSON><PERSON> học",
    "lop": 12,
    "tong_so_cau": 10,  # Giảm xuống để test nhanh
    "cau_hinh_de": [
        {
            "lesson_id": "234",
            "yeu_cau_can_dat": "Hiểu và phân biệt proton, neutron, electron theo khối lư<PERSON>, điện tích và vị trí.",
            "muc_do": [
                {
                    "loai": "Nhận biết",
                    "so_cau": 5,
                    "loai_cau": ["TN"]
                },
                {
                    "loai": "Thông hiểu", 
                    "so_cau": 3,
                    "loai_cau": ["TN"]
                },
                {
                    "loai": "<PERSON><PERSON><PERSON> dụng",
                    "so_cau": 2,
                    "loai_cau": ["TN"]
                }
            ]
        }
    ]
}

def test_exam_generation():
    """Test exam generation với format mới"""
    url = "http://127.0.0.1:8000/api/exam/generate-exam"
    
    print("Testing exam generation with new format...")
    print(f"Request data: {json.dumps(test_request, indent=2, ensure_ascii=False)}")
    
    try:
        response = requests.post(url, json=test_request, timeout=60)
        print(f"Status code: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("✅ Success!")
            print(f"Response: {json.dumps(result, indent=2, ensure_ascii=False)}")
        else:
            print("❌ Error!")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Exception: {e}")

if __name__ == "__main__":
    test_exam_generation()
