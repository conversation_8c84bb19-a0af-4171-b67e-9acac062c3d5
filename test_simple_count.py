import requests

# Test đơn giản với 5 câu hỏi
url = "http://127.0.0.1:8000/api/v1/exam/generate-exam"

payload = {
    "lesson_id": "234", 
    "mon_hoc": "<PERSON><PERSON><PERSON> học",
    "lop": 12,
    "tong_so_cau": 5,
    "cau_hinh_de": [
        {
            "bai": "Cấu tạo nguyên tử",
            "so_cau": 5,
            "noi_dung": [
                {
                    "ten_noi_dung": "Hạt cấu tạo nguyên tử",
                    "yeu_cau_can_dat": "Học sinh hiểu và phân biệt được proton, neutron và electron về khối lư<PERSON>, đi<PERSON><PERSON> tích, vị trí trong nguyên tử.",
                    "muc_do": [
                        {
                            "loai": "Nhận biết",
                            "so_cau": 5,
                            "loai_cau": ["TN"]
                        }
                    ]
                }
            ]
        }
    ]
}

headers = {
    "Content-Type": "application/json",
    "X-API-Key": "test-api-key-2024"
}

print("Đang test với 5 câu hỏi...")
response = requests.post(url, json=payload, headers=headers)

print(f"Status code: {response.status_code}")
if response.status_code == 200:
    data = response.json()
    print(f"Success: {data.get('success')}")
    print(f"Total generated: {data.get('total_generated')}")
    print(f"Number of questions: {len(data.get('questions', []))}")
    
    # Kiểm tra từng câu hỏi
    questions = data.get('questions', [])
    for i, q in enumerate(questions):
        print(f"Question {i+1}: {q.get('cau_hoi', '')[:50]}...")
else:
    print(f"Lỗi: {response.text}")
