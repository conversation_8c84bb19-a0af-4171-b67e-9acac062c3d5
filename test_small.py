import requests
import json

# Test với 5 câu trước
url = "http://127.0.0.1:8000/api/v1/exam/generate-exam-download"

payload = {
    "lesson_id": "234",
    "mon_hoc": "Hoa hoc",
    "lop": 12,
    "tong_so_cau": 5,
    "cau_hinh_de": [
        {
            "bai": "Cau tao nguyen tu",
            "so_cau": 5,
            "noi_dung": [
                {
                    "ten_noi_dung": "Hat cau tao nguyen tu",
                    "yeu_cau_can_dat": "Hoc sinh hieu va phan biet duoc proton, neutron va electron.",
                    "muc_do": [
                        {
                            "loai": "Nhan biet",
                            "so_cau": 3,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Thong hieu",
                            "so_cau": 2,
                            "loai_cau": ["TN"]
                        }
                    ]
                }
            ]
        }
    ]
}

headers = {
    "Content-Type": "application/json",
    "X-API-Key": "test-api-key-2024"
}

print("Testing with 5 questions...")

try:
    response = requests.post(url, json=payload, headers=headers, timeout=60)
    print(f"Status code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("SUCCESS!")
        print(f"Total generated: {result.get('total_generated', 0)}")
        print(f"Expected: {payload['tong_so_cau']}")
        
        actual_questions = len(result.get('questions', []))
        if actual_questions < payload['tong_so_cau']:
            print(f"MISSING QUESTIONS: {actual_questions}/{payload['tong_so_cau']}")
        else:
            print(f"ALL QUESTIONS GENERATED: {actual_questions}/{payload['tong_so_cau']}")
            
    else:
        print(f"ERROR: {response.status_code}")
        try:
            print(f"Response: {response.text}")
        except UnicodeEncodeError:
            print(f"Response contains unicode characters that cannot be displayed")
            if response.headers.get('content-type', '').startswith('application/json'):
                try:
                    error_data = response.json()
                    print(f"Error detail: {error_data.get('detail', 'Unknown error')}")
                except:
                    print("Could not parse error response as JSON")
        
except Exception as e:
    print(f"ERROR: {e}")
