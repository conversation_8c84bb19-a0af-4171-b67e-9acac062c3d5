import os
from dotenv import load_dotenv
from app.services.llm_service import LLMService

# Load environment variables
load_dotenv()

print(f"API Key from env: {os.getenv('GEMINI_API_KEY')[:20]}...")

# Test LLM service initialization
llm_service = LLMService()

print(f"LLM Service model: {llm_service.model}")

if llm_service.model:
    print("LLM Service initialized successfully!")
    
    # Test simple generation
    try:
        prompt = "Tạo 1 câu hỏi trắc nghiệm đơn giản về hóa học."
        response = llm_service.model.generate_content(prompt)
        print(f"Response type: {type(response)}")
        print(f"Response text type: {type(response.text)}")
        print(f"Response text length: {len(response.text) if response.text else 0}")
        print(f"Response text preview: {response.text[:100] if response.text else 'None'}")
        
    except Exception as e:
        print(f"Error in generation: {e}")
else:
    print("LLM Service failed to initialize!")
