import requests
import json

# Test tạo đề thi với 60 câu hỏi
url = "http://127.0.0.1:8000/api/v1/exam/generate-exam-download"

# Input từ user với 60 câu
payload = {
    "lesson_id": "234",
    "mon_hoc": "<PERSON><PERSON><PERSON> học",
    "lop": 12,
    "tong_so_cau": 60,
    "cau_hinh_de": [
        {
            "bai": "Cấu tạo nguyên tử",
            "so_cau": 60,
            "noi_dung": [
                {
                    "ten_noi_dung": "Hạt cấu tạo nguyên tử",
                    "yeu_cau_can_dat": "Học sinh hiểu và phân biệt được proton, neutron và electron về khối lượng, điện tích, vị trí trong nguyên tử.",
                    "muc_do": [
                        {
                            "loai": "Nhận biết",
                            "so_cau": 25,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Thông hiểu",
                            "so_cau": 20,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Vận dụng",
                            "so_cau": 10,
                            "loai_cau": ["TN"]
                        },
                        {
                            "loai": "Vận dụng cao",
                            "so_cau": 5,
                            "loai_cau": ["TN"]
                        }
                    ]
                }
            ]
        }
    ]
}

headers = {
    "Content-Type": "application/json",
    "X-API-Key": "test-api-key-2024"
}

print("Dang gui request tao de thi voi 60 cau...")
print(f"Payload: {json.dumps(payload, indent=2, ensure_ascii=True)}")

try:
    response = requests.post(url, json=payload, headers=headers, timeout=300)
    print(f"Status code: {response.status_code}")

    if response.status_code == 200:
        result = response.json()
        print("SUCCESS: Tao de thi thanh cong!")
        print(f"THONG KE:")
        print(f"   - Tong so cau duoc tao: {result.get('total_generated', 0)}")
        print(f"   - Tong so cau yeu cau: {payload['tong_so_cau']}")

        if 'statistics' in result:
            stats = result['statistics']
            print(f"   - Phan bo theo muc do: {stats.get('phan_bo_theo_muc_do', {})}")
            print(f"   - Phan bo theo loai: {stats.get('phan_bo_theo_loai', {})}")

        # Kiểm tra số câu thực tế
        actual_questions = len(result.get('questions', []))
        expected_questions = payload['tong_so_cau']

        if actual_questions < expected_questions:
            print(f"ERROR: THIEU CAU HOI: Chi tao duoc {actual_questions}/{expected_questions} cau")
            print("PHAN TICH CHI TIET:")

            # Đếm theo mức độ
            muc_do_count = {}
            for q in result.get('questions', []):
                muc_do = q.get('muc_do', 'Unknown')
                muc_do_count[muc_do] = muc_do_count.get(muc_do, 0) + 1

            print(f"   - So cau thuc te theo muc do: {muc_do_count}")

            # So sánh với yêu cầu
            for noi_dung in payload['cau_hinh_de'][0]['noi_dung']:
                for muc_do in noi_dung['muc_do']:
                    expected = muc_do['so_cau']
                    actual = muc_do_count.get(muc_do['loai'], 0)
                    if actual < expected:
                        print(f"   - {muc_do['loai']}: thieu {expected - actual} cau ({actual}/{expected})")
        else:
            print(f"SUCCESS: DU SO CAU: Tao duoc {actual_questions}/{expected_questions} cau")

    else:
        print(f"ERROR: {response.status_code}")
        print(f"Response: {response.text}")

except requests.exceptions.Timeout:
    print("ERROR: Timeout - Request mat qua nhieu thoi gian")
except requests.exceptions.RequestException as e:
    print(f"ERROR: Loi request: {e}")
except Exception as e:
    print(f"ERROR: Loi khong xac dinh: {e}")
