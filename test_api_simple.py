import requests
import json

# Test API đơn giản
url = "http://127.0.0.1:8000/api/v1/pdf/health"

headers = {
    "Content-Type": "application/json",
    "X-API-Key": "test-api-key-2024"
}

payload = {}

print("Testing simple API endpoint...")

try:
    response = requests.get(url, headers=headers, timeout=30)
    print(f"Status code: {response.status_code}")
    
    if response.status_code == 200:
        result = response.json()
        print("SUCCESS: API is working!")
        print(f"Results count: {len(result.get('results', []))}")
    else:
        print(f"ERROR: {response.status_code}")
        print(f"Response: {response.text}")
        
except Exception as e:
    print(f"ERROR: {e}")
