import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

# Check API key
api_key = os.getenv("GEMINI_API_KEY")
print(f"API Key loaded: {api_key[:20]}..." if api_key else "No API key found")

# Test simple HTTP request
import requests

url = "http://127.0.0.1:8000/api/v1/exam/generate-exam"
data = {
    "exam_id": "test",
    "ten_truong": "Test School",
    "mon_hoc": "Chemistry", 
    "lop": 12,
    "tong_so_cau": 1,
    "cau_hinh_de": [
        {
            "lesson_id": "234",
            "yeu_cau_can_dat": "Test requirement",
            "muc_do": [
                {
                    "loai": "Nhận biết",
                    "so_cau": 1,
                    "loai_cau": ["TN"]
                }
            ]
        }
    ]
}

try:
    print("Sending request...")
    response = requests.post(url, json=data, timeout=30)
    print(f"Status: {response.status_code}")
    
    if response.status_code != 200:
        print(f"Error response: {response.text[:500]}")
    else:
        print("Success!")
        
except Exception as e:
    print(f"Exception: {str(e)[:200]}")
