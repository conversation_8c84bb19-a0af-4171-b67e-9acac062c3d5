from docx import Document
from docx.shared import Inches, Pt
from docx.enum.text import WD_PARAGRAPH_ALIGNMENT
import sys
import os

# Thêm path để import service
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.services.exam_docx_service import ExamDocxService

def test_answer_table_with_empty_cells():
    """Test format bảng đáp án với 45 câu (để test ô trống)"""
    
    # Tạo dữ liệu giả cho 45 câu hỏi (thiếu 15 câu)
    fake_questions = []
    for i in range(45):
        fake_questions.append({
            "loai_cau": "TN",
            "dap_an": {
                "dung": chr(65 + (i % 4))  # A, B, C, D luân phiên
            }
        })
    
    # Tạo document
    doc = Document()
    
    # Tạo service
    service = ExamDocxService()
    
    # Setup document style (bao gồm số trang)
    service._setup_document_style(doc)
    
    # Thêm tiêu đề
    title_para = doc.add_paragraph()
    title_para.alignment = WD_PARAGRAPH_ALIGNMENT.CENTER
    title_run = title_para.add_run("TEST BẢNG ĐÁP ÁN 45 CÂU (15 Ô TRỐNG)")
    title_run.bold = True
    title_run.font.size = Pt(16)
    
    doc.add_paragraph()
    
    # Tạo bảng đáp án
    service._create_tn_answer_section(doc, fake_questions)
    
    # Lưu file
    filename = "test_table_45_questions_with_empty.docx"
    doc.save(filename)
    
    print(f"✅ File test đã được tạo: {filename}")
    print(f"📊 Số câu hỏi có đáp án: {len(fake_questions)}")
    print(f"📊 Số ô trống: {60 - len(fake_questions)}")
    print("\n🔍 Hãy kiểm tra:")
    print("1. Bảng vẫn có 7 hàng x 10 cột không?")
    print("2. Có đủ 60 ô (45 có đáp án + 15 trống) không?")
    print("3. Các ô từ 46-60 có để trống không?")
    print("4. Có số trang ở footer không?")
    print("5. Format có đẹp và dễ đọc không?")

if __name__ == "__main__":
    test_answer_table_with_empty_cells()
